/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "var(--color-primary)",
          light: "var(--color-primary-light)",
          lighter: "var(--color-primary-lighter)",
          dark: "var(--color-primary-dark)",
        },
        secondary: {
          DEFAULT: "var(--color-secondary)",
          dark: "var(--color-secondary-dark)",
        },
        brown: {
          DEFAULT: "var(--color-brown)",
          light: "var(--color-brown-light)",
          lighter: "var(--color-brown-lighter)",
          dark: "var(--color-brown-dark)",
        },
        cream: {
          DEFAULT: "var(--color-cream)",
          light: "var(--color-cream-light)",
        },
      },
    },
  },
  plugins: [],
};
