{"arrowParens": "always", "bracketSameLine": false, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "experimentalTernaries": false, "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "consistent", "requirePragma": false, "semi": true, "singleAttributePerLine": false, "singleQuote": false, "tabWidth": 2, "trailingComma": "all", "useTabs": false, "importOrder": ["^react$", "^react-dom(.*)$", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^@components/(.*)$", "^@commons/(.*)$", "^@base/(.*)$", "^@screens/(.*)$", "^@lib/(.*)$", "^@services/(.*)$", "^@hooks/(.*)$", "^@utils/(.*)$", "^@constants/(.*)$", "^@styles/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderParserPlugins": ["typescript", "decorators-legacy"], "overrides": [{"files": "*.tsx", "options": {"parser": "babel-ts"}}], "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"]}