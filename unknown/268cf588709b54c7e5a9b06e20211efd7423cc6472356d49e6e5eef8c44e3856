import { useEffect, useRef, useState } from "react";

import { useAuthStore } from "@store/useAuthStore";
import { useNavigate, useSearchParams } from "react-router-dom";

import type { User } from "@/constants";
import { useGoogleCallback } from "@/services";
import { getUserProfile } from "@/utils";

import { useToast } from "@components/commons/toast";

export const useGoogleCallbackHandler = () => {
  const [searchParams] = useSearchParams();
  const googleCallbackMutation = useGoogleCallback();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { handleLoginSuccess } = useAuthStore();
  const [userData, setUserData] = useState<User | null>(getUserProfile());
  const isCallbackProcessedRef = useRef(false);
  const hasLoggedResponse = useRef(false);

  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === "userProfile") {
        setUserData(getUserProfile());
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  useEffect(() => {
    const codeParam = searchParams.get("code");
    if (codeParam && !isCallbackProcessedRef.current) {
      isCallbackProcessedRef.current = true;

      const code = codeParam.split("&")[0];
      googleCallbackMutation
        .mutateAsync(code)
        .then((response) => {
          const userProfile = getUserProfile();
          setUserData(userProfile);

          toast({
            title: "Success",
            description: "You have successfully logged in with Google",
            variant: "default",
          });

          handleLoginSuccess(userProfile);

          setTimeout(() => {
            if (userProfile && userProfile.phone) {
              navigate("/", { replace: true });
            } else {
              navigate("/register", { replace: true });
            }
          }, 500);
          return response;
        })
        .catch((error) => {
          toast({
            title: "Error",
            description:
              "Failed to authenticate with Google. Please try again.",
            variant: "destructive",
          });
          console.error("Google callback error:", error);
          isCallbackProcessedRef.current = false;

          const { handleLogout } = useAuthStore.getState();
          handleLogout();

          setTimeout(() => {
            navigate("/", { replace: true });
          }, 500);
        });
    }
  }, [
    searchParams,
    googleCallbackMutation,
    navigate,
    toast,
    handleLoginSuccess,
  ]);

  useEffect(() => {
    if (
      googleCallbackMutation.data &&
      googleCallbackMutation.isSuccess &&
      !hasLoggedResponse.current
    ) {
      console.log(googleCallbackMutation.data);
      hasLoggedResponse.current = true;
    }
  }, [googleCallbackMutation.data, googleCallbackMutation.isSuccess]);

  return {
    isProcessing: googleCallbackMutation.isPending,
    isSuccess: googleCallbackMutation.isSuccess,
    isError: googleCallbackMutation.isError,
    error: googleCallbackMutation.error,
    userData,
  };
};
