import React from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { UserIcon, CreditCardIcon, HomeIcon, LogOutIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Logout } from "@components/logout";
import { Tabs } from "@/components/commons/tabs";
import type { TabItem } from "@/components/commons/tabs/types";
import { AccountDetail } from "@/components/account-detail";
import { PaymentMethods } from "@/components/payment-methods";
import { Address } from "@components/address/address.tsx";

export const SettingAccount = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = React.useState("account-details");

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Define all tab items including logout
  const tabItems: TabItem[] = [
    {
      value: "account-details",
      label: "รายละเอียดบัญชี",
      iconBefore: <UserIcon className="mr-2 h-4 w-4" />,
    },
    {
      value: "payment-methods",
      label: "วิธีชำระเงิน",
      iconBefore: <CreditCardIcon className="mr-2 h-4 w-4" />,
    },
    {
      value: "shipping-address",
      label: "ที่อยู่จัดส่ง",
      iconBefore: <HomeIcon className="mr-2 h-4 w-4" />,
    },
    {
      value: "logout",
      label: "ออกจากระบบ",
      iconBefore: (
        <LogOutIcon
          className={cn(
            "mr-2 h-4 w-4",
            activeTab !== "logout" && "text-red-500",
          )}
        />
      ),
    },
  ];

  // Custom render function for tab buttons
  const renderTabButton = (
    item: TabItem,
    isActive: boolean,
    onClick: () => void,
  ) => {
    // Special handling for logout tab
    if (item.value === "logout") {
      return (
        <Logout key={item.value} onLogout={onClick}>
          <button
            role="tab"
            aria-selected={isActive}
            disabled={item.disabled}
            className={cn(
              "flex items-center justify-center p-2 md:w-full md:justify-start md:px-4 md:py-2",
              "font-regular rounded-md text-sm",
              isActive
                ? "bg-primary text-white"
                : "text-red-500 hover:bg-gray-200",
            )}
          >
            <span className="h-4 w-4">{item.iconBefore}</span>
            <span className="hidden md:ml-2 md:inline">{item.label}</span>
            {item.iconAfter && (
              <span className="md:ml-2">{item.iconAfter}</span>
            )}
          </button>
        </Logout>
      );
    }

    // Default rendering for regular tabs
    return (
      <button
        role="tab"
        aria-selected={isActive}
        disabled={item.disabled}
        className={cn(
          "flex items-center justify-center p-2 md:w-full md:justify-start md:px-4 md:py-2",
          "font-regular min-h-[32px] min-w-[100px] rounded-md text-sm",
          isActive ? "bg-primary text-white" : "hover:bg-gray-200",
        )}
        onClick={onClick}
      >
        <span className="h-4 w-4">{item.iconBefore}</span>
        <span className="hidden md:ml-2 md:inline">{item.label}</span>
        {item.iconAfter && <span className="md:ml-2">{item.iconAfter}</span>}
      </button>
    );
  };

  const renderTabContent = (activeTab: string) => {
    switch (activeTab) {
      case "account-details":
        return (
          <div>
            <AccountDetail />
          </div>
        );
      case "payment-methods":
        return (
          <div>
            <PaymentMethods />
          </div>
        );
      case "shipping-address":
        return (
          <div>
            <Address />
          </div>
        );
      case "logout":
        return (
          <div className="p-4">
            <Logout>
              <div className="cursor-pointer">
                <p>{t("myAccount.logoutContent")}</p>
              </div>
            </Logout>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      <h3 className="mb-4 text-lg font-medium">
        {t("myAccount.favoriteSettingsTitle")}
      </h3>

      <Tabs
        items={tabItems}
        value={activeTab}
        onChange={handleTabChange}
        orientation="vertical"
        renderTabButton={renderTabButton}
        renderTabContent={renderTabContent}
      />
    </div>
  );
};
