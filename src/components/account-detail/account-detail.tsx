import React from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
} from "@/components/commons/base/card";
import { InputField } from "@/components/commons/form/input-field";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/plain.css";
import { cn } from "@/lib/utils";
import type { AccountDetailFormData } from "./types";

export const AccountDetail: React.FC = () => {
  const {
    register,
    formState: { errors },
  } = useForm<AccountDetailFormData>();

  const [phoneNumber, setPhoneNumber] = React.useState("");

  return (
    <Card className="md:border-primary-border-card w-full sm:border-none md:border">
      <CardHeader>
        <CardTitle className="font-regular text-[23px] text-black">
          รายละเอียดบัญชี
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <InputField
            id="email"
            label="อีเมล"
            type="email"
            placeholder="<EMAIL>"
            className="w-full lg:w-1/2"
            {...register("email")}
            error={errors.email?.message}
            autoComplete="email"
          />

          <InputField
            id="password"
            label="รหัสผ่าน"
            type="password"
            className="w-full lg:w-1/2"
            {...register("password")}
            error={errors.password?.message}
            autoComplete="current-password"
          />

          <div className="mb-4">
            <label
              htmlFor="mobileNumber"
              className="mb-1 block text-sm font-medium text-black"
            >
              เบอร์โทรศัพท์
            </label>
            <div className="w-full lg:w-1/2">
              <PhoneInput
                country={"th"}
                value={phoneNumber}
                onChange={setPhoneNumber}
                inputClass={cn(
                  "bg-white focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "h-9 rounded-md border px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none md:text-sm",
                )}
                inputProps={{
                  id: "mobileNumber",
                  name: "mobileNumber",
                }}
              />
            </div>
            {errors.mobileNumber && (
              <p className="mt-1 text-xs text-red-600">
                {errors.mobileNumber.message}
              </p>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
