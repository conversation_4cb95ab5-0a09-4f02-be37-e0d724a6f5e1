import { useEffect, useRef, useState } from "react";

import gsap from "gsap";

export const useScrollDetection = () => {
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return scrolled;
};

export const useMenuAnimation = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (menuOpen) {
      document.body.classList.add("menu-open");
      document.body.style.overflow = "hidden";

      // Animate menu opening with GSAP - full screen fade in
      if (mobileMenuRef.current) {
        mobileMenuRef.current.classList.remove("hidden");
        gsap.set(mobileMenuRef.current, {
          opacity: 0,
          scale: 1,
        });
        gsap.to(mobileMenuRef.current, {
          opacity: 1,
          scale: 1,
          duration: 0.3,
          ease: "power2.out",
        });
      }
    } else {
      // Animate menu closing with GSAP - fade out
      if (mobileMenuRef.current) {
        gsap.to(mobileMenuRef.current, {
          opacity: 0,
          scale: 0.95,
          duration: 0.3,
          ease: "power2.in",
          onComplete: () => {
            if (mobileMenuRef.current) {
              mobileMenuRef.current.classList.add("hidden");
            }
            document.body.classList.remove("menu-open");
            document.body.style.overflow = "unset";
          },
        });
      } else {
        document.body.classList.remove("menu-open");
        document.body.style.overflow = "unset";
      }
    }
  }, [menuOpen]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return { menuOpen, toggleMenu, mobileMenuRef };
};
