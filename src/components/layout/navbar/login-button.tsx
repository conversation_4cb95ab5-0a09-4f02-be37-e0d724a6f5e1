import React, { useState, useRef } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { UserIcon } from "lucide-react";
import { useTranslation } from "@hooks/useTranslation";
import { useAuth } from "@store/useAuthStore";
import { useDropdownAnimation, useClickOutside } from "@hooks/useDropdown";
import { Logout } from "@components/logout";
import type {
  IconWithTextProps,
  MenuItemProps,
  LoginButtonProps,
} from "./types";

const IconWithText: React.FC<IconWithTextProps> = ({ text }) => (
  <>
    <UserIcon />
    <div className="md:hidden">
      <span className="text-[length:var(--font-size-md)] font-medium">
        {text}
      </span>
    </div>
  </>
);

const MenuItem: React.FC<MenuItemProps> = ({
  to,
  onClick,
  children,
  className = "text-black",
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const baseClassName = `block px-4 py-2 text-sm ${className} hover:bg-gray-100`;

  const isMyAccountPage = location.pathname === "/my-account";

  if (to) {
    if (isMyAccountPage && to.startsWith("/my-account?tab=")) {
      const tabValue = to.split("=")[1];
      return (
        <button
          onClick={() =>
            navigate(`/my-account?tab=${tabValue}`, { replace: true })
          }
          className={`${baseClassName} w-full text-left`}
          role="menuitem"
        >
          {children}
        </button>
      );
    }

    return (
      <Link to={to} className={baseClassName} role="menuitem">
        {children}
      </Link>
    );
  }

  return (
    <button
      onClick={onClick ? () => onClick() : undefined}
      className={`${baseClassName} w-full text-left`}
      role="menuitem"
    >
      {children}
    </button>
  );
};

const LoginButton: React.FC<LoginButtonProps> = ({ onClick }) => {
  const { t } = useTranslation();
  const { isAuthenticated } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownMenuRef = useRef<HTMLDivElement>(null);

  useDropdownAnimation({ isOpen, dropdownMenuRef });
  useClickOutside({ ref: dropdownRef, callback: () => setIsOpen(false) });

  const menuItems = [
    { to: "/my-account?tab=overview", label: "navbar.overview" },
    { to: "/my-account?tab=orders", label: "navbar.orders" },
    { to: "/my-account?tab=favorites", label: "navbar.favorites" },
    {
      to: "/my-account?tab=favorite-settings",
      label: "navbar.accountSettings",
    },
  ];

  if (!isAuthenticated) {
    return (
      <Link
        to="/login"
        className="flex items-center space-x-2 py-2 text-sm font-medium md:px-4"
        onClick={onClick ? () => onClick() : undefined}
      >
        <IconWithText text={t("navbar.login")} />
      </Link>
    );
  }

  if (onClick) {
    return (
      <Link
        to="/my-account"
        className="flex items-center space-x-2 py-2 text-sm font-medium md:px-4"
        onClick={onClick ? () => onClick() : undefined}
      >
        <IconWithText text={t("navbar.myAccount")} />
      </Link>
    );
  }

  // Desktop view with dropdown menu
  return (
    <div className="relative z-50" ref={dropdownRef}>
      <button
        onClick={() => {
          setIsOpen(!isOpen);
          if (onClick) {
            (onClick as () => void)();
          }
        }}
        className="flex cursor-pointer items-center space-x-2 py-2 text-sm font-medium md:px-4"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <IconWithText text={t("navbar.myAccount")} />
      </button>

      <div
        ref={dropdownMenuRef}
        className="ring-opacity-5 absolute right-0 z-50 mt-2 w-48 rounded-md bg-white shadow-lg"
        style={{ display: "none" }}
      >
        <div role="menu" aria-orientation="vertical">
          {menuItems.map((item) => (
            <MenuItem key={item.label} to={item.to}>
              {t(item.label)}
            </MenuItem>
          ))}

          <Logout
            onLogout={() => {
              if (onClick) {
                (onClick as () => void)();
              }
            }}
          >
            <MenuItem
              onClick={() => {
                setIsOpen(false);
              }}
              className="cursor-pointer text-red-600"
            >
              {t("navbar.logout")}
            </MenuItem>
          </Logout>
        </div>
      </div>
    </div>
  );
};

export default LoginButton;
