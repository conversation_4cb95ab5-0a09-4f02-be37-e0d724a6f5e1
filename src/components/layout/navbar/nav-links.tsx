import { Link } from "react-router-dom";
import { useTranslation } from "@/hooks/useTranslation";
import type { NavLinksProps } from "./types";

const NavLinks = ({ mobile = false, onClick }: NavLinksProps) => {
  const { t } = useTranslation();

  const links = [
    { href: "/", label: t("navbar.home") },
    { href: "/products", label: t("navbar.products") },
  ];

  return (
    <nav
      className={
        mobile
          ? "flex flex-col space-y-4 border-b border-black"
          : "flex space-x-12"
      }
    >
      {links.map((link, index) => (
        <Link
          key={link.href}
          to={link.href}
          onClick={onClick}
          className={`nav-link hover:text-primary-text-rest text-black ${
            mobile
              ? "block border-b border-transparent py-2 text-left text-lg"
              : "text-lg font-medium tracking-wider uppercase"
          } ${index === 0 && mobile ? "border-cream-stroke-1-rest" : ""}`}
        >
          {link.label}
        </Link>
      ))}
    </nav>
  );
};

export default NavLinks;
