import { Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLocationDot,
  faPhone,
  faEnvelope,
} from "@fortawesome/free-solid-svg-icons";
import { SocialIcon } from "./social-icon";

export default function Footer() {
  const sponsorshipPackage = {
    title: "Sponsorship Package",
    phone: "************",
    email: "<EMAIL>",
  };

  return (
    <footer className="bg-primary-lighter text-mojo-bronw-6 pt-8">
      <div className="container mx-auto max-w-screen-xl px-4">
        {/* Logo section */}
        <div className="mb-6 text-center">
          <Link to="/" className="inline-block">
            <div className="flex flex-col items-center">
              <img src="/images/logo.png" alt="Music Note" className="h-14" />
              <span className="mt-2 text-xl font-bold">
                <PERSON><PERSON>
              </span>
            </div>
          </Link>
        </div>

        {/* Mobile layout - stacked sections */}
        <div className="flex flex-col md:hidden">
          {/* Contact Info Section */}
          <div className="mb-6 flex flex-col items-center text-center">
            <h3 className="mb-3 text-lg font-bold">
              Mojo Living Co., Ltd. (Head office)
            </h3>
            <div className="space-y-3">
              {/* Location */}
              <div className="flex items-start justify-center">
                <FontAwesomeIcon
                  icon={faLocationDot}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <div className="text-center text-sm">
                  <div>607 Thanon Asok - Din Daeng, Din Daeng,</div>
                  <div>Bangkok 10400</div>
                </div>
              </div>

              {/* Email */}
              <div className="flex items-center justify-center">
                <FontAwesomeIcon
                  icon={faEnvelope}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <a
                  href="mailto:<EMAIL>"
                  className="text-sm hover:underline"
                >
                  <EMAIL>
                </a>
              </div>

              {/* Phone */}
              <div className="flex items-center justify-center">
                <FontAwesomeIcon
                  icon={faPhone}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <span className="text-sm">************</span>
              </div>
            </div>
          </div>

          {/* Sponsorship Package Section */}
          <div className="mb-6 flex flex-col items-center text-center">
            <h3 className="mb-3 text-lg font-bold">
              {sponsorshipPackage.title}
            </h3>
            <div className="space-y-3">
              {/* Email */}
              <div className="flex items-center justify-center">
                <FontAwesomeIcon
                  icon={faEnvelope}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <span className="text-sm">{sponsorshipPackage.email}</span>
              </div>

              {/* Phone */}
              <div className="flex items-center justify-center">
                <FontAwesomeIcon
                  icon={faPhone}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <span className="text-sm">{sponsorshipPackage.phone}</span>
              </div>
            </div>
          </div>

          {/* Follow Us Section */}
          <div className="mb-6 flex flex-col items-center text-center">
            <h3 className="mb-3 text-lg font-bold">Follow Us</h3>
            <div className="flex justify-center gap-4">
              <SocialIcon
                href="https://www.tiktok.com/@mojo.muse.managem"
                icon="tiktok"
              />
              <SocialIcon
                href="https://www.facebook.com/mojomusemanagement/"
                icon="facebook"
              />
              <SocialIcon href="https://x.com/MojoMuse_MMM" icon="twitter" />
              <SocialIcon
                href="https://www.instagram.com/mojo_muse_management/"
                icon="instagram"
              />
              <SocialIcon
                href="https://www.youtube.com/channel/UCQLybt2G6Z1nJqQKzlcCqLg"
                icon="youtube"
              />
              <SocialIcon href="https://lin.ee/fyUqK7L" icon="line" />
            </div>
          </div>
        </div>

        {/* Desktop layout - 3 columns */}
        <div className="hidden md:mb-8 md:grid md:grid-cols-3 md:gap-8">
          {/* Contact Info Column */}
          <div className="flex flex-col">
            <h3 className="mb-4 text-lg font-bold">
              Mojo Living Co., Ltd. (Head office)
            </h3>
            <div className="space-y-3">
              {/* Location */}
              <div className="flex items-start">
                <FontAwesomeIcon
                  icon={faLocationDot}
                  className="text-mojo-bronw-6 mt-1 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <p className="text-sm">
                  607 Thanon Asok - Din Daeng, Din Daeng, Bangkok 10400
                </p>
              </div>

              {/* Email */}
              <div className="flex items-center">
                <FontAwesomeIcon
                  icon={faEnvelope}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <a
                  href="mailto:<EMAIL>"
                  className="text-sm hover:underline"
                >
                  <EMAIL>
                </a>
              </div>

              {/* Phone */}
              <div className="flex items-center">
                <FontAwesomeIcon
                  icon={faPhone}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <span className="text-sm">************</span>
              </div>
            </div>
          </div>

          {/* Sponsorship Package Section */}
          <div className="flex flex-col items-center">
            <h3 className="mb-4 text-lg font-bold">
              {sponsorshipPackage.title}
            </h3>
            <div className="space-y-3">
              {/* Email */}
              <div className="flex items-center">
                <FontAwesomeIcon
                  icon={faEnvelope}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <span className="text-sm">{sponsorshipPackage.email}</span>
              </div>

              {/* Phone */}
              <div className="flex items-center">
                <FontAwesomeIcon
                  icon={faPhone}
                  className="text-mojo-bronw-6 mr-3 flex-shrink-0"
                  style={{ width: "18px", height: "18px" }}
                />
                <span className="text-sm">{sponsorshipPackage.phone}</span>
              </div>
            </div>
          </div>

          {/* Follow Us Section */}
          <div className="flex flex-col items-center">
            <h3 className="mb-4 text-lg font-bold">Follow Us</h3>
            <div className="flex justify-center gap-4">
              <SocialIcon
                href="https://www.tiktok.com/@mojo.muse.managem"
                icon="tiktok"
              />
              <SocialIcon
                href="https://www.facebook.com/mojomusemanagement/"
                icon="facebook"
              />
              <SocialIcon href="https://x.com/MojoMuse_MMM" icon="twitter" />
              <SocialIcon
                href="https://www.instagram.com/mojo_muse_management/"
                icon="instagram"
              />
              <SocialIcon
                href="https://www.youtube.com/channel/UCQLybt2G6Z1nJqQKzlcCqLg"
                icon="youtube"
              />
              <SocialIcon href="https://lin.ee/fyUqK7L" icon="line" />
            </div>
          </div>
        </div>
      </div>

      {/* Separator Line */}
      <div className="mb-3 h-px w-full bg-[#3e332a]/20"></div>

      {/* Copyright */}
      <div className="py-3 text-center text-xs">
        © {new Date().getFullYear()} Mojo Muse Management
      </div>
    </footer>
  );
}
