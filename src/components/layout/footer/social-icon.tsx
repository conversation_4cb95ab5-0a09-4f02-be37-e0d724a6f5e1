import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTiktok,
  faFacebookF,
  faXTwitter,
  faInstagram,
  faYoutube,
  faLine,
} from "@fortawesome/free-brands-svg-icons";

export interface SocialIconProps {
  href: string;
  icon: "tiktok" | "facebook" | "twitter" | "instagram" | "youtube" | "line";
}

export function SocialIcon({ href, icon }: SocialIconProps) {
  // Map icon names to Font Awesome icons
  const iconMap = {
    tiktok: faTiktok,
    facebook: faFacebookF,
    twitter: faXTwitter,
    instagram: faInstagram,
    youtube: faYoutube,
    line: faLine,
  };

  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="flex h-10 w-10 items-center justify-center rounded-full transition-transform hover:scale-110 focus:ring-2 focus:ring-[#674636] focus:ring-offset-2 focus:outline-none"
      aria-label={`Visit our ${icon} page`}
    >
      <FontAwesomeIcon
        icon={iconMap[icon]}
        className="text-mojo-bronw-6"
        style={{ width: "24px", height: "24px" }}
      />
    </a>
  );
}
