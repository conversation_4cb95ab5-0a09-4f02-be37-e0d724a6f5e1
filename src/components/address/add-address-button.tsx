import React from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

interface AddAddressButtonProps {
  onClick: () => void;
}

export const AddAddressButton: React.FC<AddAddressButtonProps> = ({
  onClick,
}) => {
  const { t } = useTranslation();

  return (
    <button
      onClick={onClick}
      className="border-primary hover:border-primary/80 hover:bg-primary/5 group mb-3 min-h-[100px] w-full rounded-[16px] border p-4 transition-colors duration-200"
    >
      <div className="flex items-center justify-center gap-3">
        <div className="group-hover:bg-primary/20 flex h-8 w-8 items-center justify-center rounded-full transition-colors">
          <Plus className="text-primary h-4 w-4" />
        </div>
        <span className="text-primary font-regular text-sm">
          {t("address.addNew")}
        </span>
      </div>
    </button>
  );
};
