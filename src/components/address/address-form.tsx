import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/commons/base/dialog";
import { Button } from "@/components/commons/base/button";
import { Label } from "@/components/commons/base/label";
import { Input } from "@/components/commons/base/input";
import { Textarea } from "@/components/commons/base/textarea";
import { useTranslation } from "@/hooks/useTranslation";
import { Spinner } from "@/components/commons/loading-spinner";
import { cn } from "@/lib/utils";
import type { Address, AddressFormData } from "@/types/address";
import { addressSchema } from "@/types/address";

interface AddressFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AddressFormData) => Promise<void>;
  initialData?: Address;
  isEdit?: boolean;
}

export const AddressForm: React.FC<AddressFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEdit = false,
}) => {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<AddressFormData>({
    resolver: zodResolver(addressSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      phone: "",
      address_line1: "",
      address_line2: "",
      sub_district: "",
      district: "",
      city: "",
      province: "",
      postal_code: "",
      country: "ประเทศไทย",
      delivery_instructions: "",
      is_default: false,
    },
  });

  useEffect(() => {
    if (isOpen) {
      if (initialData && isEdit) {
        setValue("first_name", initialData.first_name);
        setValue("last_name", initialData.last_name);
        setValue("phone", initialData.phone);
        setValue("address_line1", initialData.address_line1);
        setValue("address_line2", initialData.address_line2);
        setValue("sub_district", initialData.sub_district);
        setValue("district", initialData.district);
        setValue("city", initialData.city);
        setValue("province", initialData.province);
        setValue("postal_code", initialData.postal_code);
        setValue("country", initialData.country);
        setValue("delivery_instructions", initialData.delivery_instructions);
        setValue("is_default", initialData.is_default);
      } else {
        reset({
          first_name: "",
          last_name: "",
          phone: "",
          address_line1: "",
          address_line2: "",
          sub_district: "",
          district: "",
          city: "",
          province: "",
          postal_code: "",
          country: "ประเทศไทย",
          delivery_instructions: "",
          is_default: false,
        });
      }
    }
  }, [isOpen, initialData, isEdit, setValue, reset]);

  const handleFormSubmit = async (data: AddressFormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      onClose();
    } catch (error) {
      console.error("Error submitting address:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-h-[90%] w-[90%] max-w-[90%] overflow-y-auto rounded-[16px] border-none lg:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? t("address.form.editTitle") : t("address.form.addTitle")}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* Name Fields */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* First Name */}
            <div>
              <Label htmlFor="first_name" className="text-sm font-medium">
                {t("address.form.firstName")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="first_name"
                {...register("first_name")}
                className={cn(
                  errors.first_name
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.firstNamePlaceholder")}
              />
              {errors.first_name && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.first_name.message}
                </p>
              )}
            </div>

            {/* Last Name */}
            <div>
              <Label htmlFor="last_name" className="text-sm font-medium">
                {t("address.form.lastName")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="last_name"
                {...register("last_name")}
                className={cn(
                  errors.last_name
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.lastNamePlaceholder")}
              />
              {errors.last_name && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.last_name.message}
                </p>
              )}
            </div>
          </div>

          {/* Phone */}
          <div>
            <Label htmlFor="phone" className="text-sm font-medium">
              {t("address.form.phone")} <span className="text-red-500">*</span>
            </Label>
            <Input
              id="phone"
              {...register("phone")}
              className={cn(
                errors.phone
                  ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                  : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                "mt-1 bg-white",
              )}
              placeholder={t("address.form.phonePlaceholder")}
            />
            {errors.phone && (
              <p className="mt-1 text-xs text-red-600">
                {errors.phone.message}
              </p>
            )}
          </div>

          {/* Address Line 1 */}
          <div>
            <Label htmlFor="address_line1" className="text-sm font-medium">
              {t("address.form.addressLine1")}{" "}
              <span className="text-red-500">*</span>
            </Label>
            <Input
              id="address_line1"
              {...register("address_line1")}
              className={cn(
                errors.address_line1
                  ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                  : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                "mt-1 bg-white",
              )}
              placeholder={t("address.form.addressLine1Placeholder")}
            />
            {errors.address_line1 && (
              <p className="mt-1 text-xs text-red-600">
                {errors.address_line1.message}
              </p>
            )}
          </div>

          {/* Address Line 2 */}
          <div>
            <Label htmlFor="address_line2" className="text-sm font-medium">
              {t("address.form.addressLine2")}
            </Label>
            <Input
              id="address_line2"
              {...register("address_line2")}
              className="focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest mt-1 border-gray-300 bg-white"
              placeholder={t("address.form.addressLine2Placeholder")}
            />
          </div>

          {/* Grid for location fields */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Sub District */}
            <div>
              <Label htmlFor="sub_district" className="text-sm font-medium">
                {t("address.form.sub_district")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="sub_district"
                {...register("sub_district")}
                className={cn(
                  errors.sub_district
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.subdistrictPlaceholder")}
              />
              {errors.sub_district && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.sub_district.message}
                </p>
              )}
            </div>

            {/* District */}
            <div>
              <Label htmlFor="district" className="text-sm font-medium">
                {t("address.form.district")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="district"
                {...register("district")}
                className={cn(
                  errors.district
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.districtPlaceholder")}
              />
              {errors.district && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.district.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* City */}
            <div>
              <Label htmlFor="city" className="text-sm font-medium">
                {t("address.form.city")} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="city"
                {...register("city")}
                className={cn(
                  errors.city
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.cityPlaceholder")}
              />
              {errors.city && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.city.message}
                </p>
              )}
            </div>

            {/* Province */}
            <div>
              <Label htmlFor="province" className="text-sm font-medium">
                {t("address.form.province")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="province"
                {...register("province")}
                className={cn(
                  errors.province
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.provincePlaceholder")}
              />
              {errors.province && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.province.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Postal Code */}
            <div>
              <Label htmlFor="postal_code" className="text-sm font-medium">
                {t("address.form.postalCode")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="postal_code"
                {...register("postal_code")}
                className={cn(
                  errors.postal_code
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.postalCodePlaceholder")}
                maxLength={5}
              />
              {errors.postal_code && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.postal_code.message}
                </p>
              )}
            </div>

            {/* Country */}
            <div>
              <Label htmlFor="country" className="text-sm font-medium">
                {t("address.form.country")}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="country"
                {...register("country")}
                className={cn(
                  errors.country
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
                  "mt-1 bg-white",
                )}
                placeholder={t("address.form.countryPlaceholder")}
              />
              {errors.country && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.country.message}
                </p>
              )}
            </div>
          </div>

          {/* Delivery Instructions */}
          <div>
            <Label
              htmlFor="delivery_instructions"
              className="text-sm font-medium"
            >
              {t("address.form.deliveryInstructions")}
            </Label>
            <Textarea
              id="delivery_instructions"
              {...register("delivery_instructions")}
              className="focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest mt-1 border-gray-300 bg-white"
              placeholder={t("address.form.deliveryInstructionsPlaceholder")}
              rows={3}
            />
            {errors.delivery_instructions && (
              <p className="mt-1 text-xs text-red-600">
                {errors.delivery_instructions.message}
              </p>
            )}
          </div>

          {/* Set as Default */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="is_default"
              {...register("is_default")}
              className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
            />
            <Label htmlFor="is_default" className="text-sm font-medium">
              {t("address.form.setAsDefault")}
            </Label>
          </div>

          <DialogFooter className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              {t("address.form.cancel")}
            </Button>
            <Button
              type="submit"
              className="text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" />
                  {isEdit
                    ? t("address.form.updating")
                    : t("address.form.adding")}
                </>
              ) : isEdit ? (
                t("address.form.update")
              ) : (
                t("address.form.add")
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
