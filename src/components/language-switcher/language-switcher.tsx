import { useState, useRef } from "react";
import * as CountryFlags from "country-flag-icons/react/3x2";
import { cn } from "@/lib/utils";
import { LANGUAGE_OPTIONS } from "./constants";
import { getCurrentLanguageOption, handleLanguageChange } from "./helpers";
import { useDropdownAnimation, useClickOutside } from "@hooks/useDropdown";
import { useLanguageStore } from "@store/useLanguageStore";

const LanguageSwitcher = () => {
  const { currentLanguage, setLanguage } = useLanguageStore();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownMenuRef = useRef<HTMLDivElement>(null);

  useDropdownAnimation({ isOpen, dropdownMenuRef });
  useClickOutside({ ref: dropdownRef, callback: () => setIsOpen(false) });

  return (
    <div className={cn("relative z-50")} ref={dropdownRef}>
      <span className="font-semibold"></span>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "bg-primary-bg-surface-active hover:bg-primary-bg-surface-hover flex cursor-pointer items-center space-x-2 rounded-md px-2 py-1 text-sm font-medium text-white backdrop-blur-sm transition md:bg-transparent md:px-4 md:py-2",
        )}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span className={cn("md:scale-150")}>
          {(() => {
            const option = getCurrentLanguageOption(currentLanguage);
            const FlagComponent =
              CountryFlags[option.countryCode as keyof typeof CountryFlags];
            return <FlagComponent className={cn("h-4 w-6 rounded-[2px]")} />;
          })()}
        </span>
      </button>

      <div
        ref={dropdownMenuRef}
        className={cn(
          "ring-opacity-5 absolute right-0 z-50 mt-2 w-40 rounded-md bg-white shadow-lg",
        )}
        style={{ minWidth: "180px", display: "none" }}
      >
        <div role="menu" aria-orientation="vertical">
          {LANGUAGE_OPTIONS.map((lang) => (
            <button
              key={lang.code}
              onClick={() =>
                handleLanguageChange(lang.code, setLanguage, setIsOpen)
              }
              className={cn(
                "flex w-full items-center px-4 py-3 text-left text-sm transition",
                currentLanguage === lang.code
                  ? "bg-primary text-white"
                  : "hover:bg-primary-bg-surface-hover text-black hover:text-white",
              )}
              role="menuitem"
            >
              <span className={cn("mr-3 inline-block w-6")}>
                {(() => {
                  const FlagComponent =
                    CountryFlags[lang.countryCode as keyof typeof CountryFlags];
                  return <FlagComponent className={cn("h-4 w-6")} />;
                })()}
              </span>
              <span>{lang.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LanguageSwitcher;
