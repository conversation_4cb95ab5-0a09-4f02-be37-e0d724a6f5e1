import React, { useState } from "react";
import { CancelDialog } from "@/components/commons";
import { useAuth } from "@/store/useAuthStore";
import type { LogoutProps } from "./types";

export const Logout: React.FC<LogoutProps> = ({
  children,
  onLogout,
  cancelText = "ยกเลิก",
  confirmText = "ออกจากระบบ",
}) => {
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const { logout } = useAuth();

  const handleLogoutClick = () => {
    setShowLogoutDialog(true);
  };

  const handleCancel = () => {
    setShowLogoutDialog(false);
  };

  const handleConfirm = () => {
    logout();
    setShowLogoutDialog(false);
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <>
      <div onClick={handleLogoutClick}>{children}</div>

      <CancelDialog
        open={showLogoutDialog}
        onOpenChange={setShowLogoutDialog}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
        cancelText={cancelText}
        confirmText={confirmText}
      />
    </>
  );
};
