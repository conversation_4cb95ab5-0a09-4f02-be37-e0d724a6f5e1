import * as React from "react";
import { Card } from "@/components/commons/base/card";
import { cn } from "@/lib/utils";

interface LoginRegisterCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function LoginRegisterCard({
  children,
  className,
  ...props
}: LoginRegisterCardProps) {
  return (
    <div className="flex w-full overflow-hidden rounded-xl shadow-2xl/30">
      {/* Left side - Content */}
      <div className="bg-primary-bg-card w-full p-[32px] md:w-1/2">
        <Card
          className={cn(
            "border-none p-0 shadow-none md:shadow-none",
            className,
          )}
          {...props}
        >
          {children}
        </Card>
      </div>

      {/* Right side - Image */}
      <div className="relative hidden md:block md:w-1/2">
        <img
          src="/images/hero-image.jpg"
          alt="Hero"
          className="absolute inset-0 h-full w-full object-cover object-[75%]"
        />
      </div>
    </div>
  );
}
