import { PaymentMethodType } from "@/types/payment-methods";
import type {
  AlipayFormData,
  BasePaymentMethod,
  CreditCardFormData,
  TrueMoneyFormData,
} from "@/types/payment-methods";

// Legacy type for backward compatibility
export interface PaymentMethod extends BasePaymentMethod {
  cardBrand?: "visa" | "mastercard" | "jcb" | "amex";
  cardNumber?: string;
  expiryDate?: string;
  holderName?: string;
  isMain?: boolean;
}

export interface PaymentMethodsProps {
  paymentMethods?: BasePaymentMethod[];
}

export interface PaymentMethodFormProps {
  type: PaymentMethodType;
  initialData?: Partial<
    CreditCardFormData | TrueMoneyFormData | AlipayFormData
  >;
  isEdit: boolean;
  onSubmit: (
    type: PaymentMethodType,
    data: CreditCardFormData | TrueMoneyFormData | AlipayFormData,
  ) => void;
  onCancel: () => void;
}
