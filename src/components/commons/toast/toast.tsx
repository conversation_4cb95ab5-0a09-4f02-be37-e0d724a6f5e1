import { toast as sonnerToast } from "sonner";
import { Toaster } from "@/components/commons/base";

export { Toaster };

export interface ToastOptions {
  title?: string;

  description?: string;
  variant?: "default" | "destructive";
}

export const useToast = () => {
  const toast = (options: ToastOptions) => {
    const { title, description, variant = "default" } = options;

    if (variant === "destructive") {
      return sonnerToast.error(title, { description });
    }

    return sonnerToast(title, { description });
  };

  return { toast };
};
