import { SquareMinus, <PERSON>P<PERSON> } from "lucide-react";
import React from "react";
import "./style.css";

interface Props {
  amount: number;
  isEnabled: boolean;
  handleMinusClick: (currentAmount: number) => void;
  handlePlusClick: (currentAmount: number) => void;
}

const QuantitySelector: React.FC<Props> = ({
  amount,
  isEnabled,
  handleMinusClick,
  handlePlusClick,
}) => {
  return (
    <>
      <div className="flex flex-row items-center gap-4">
        <div>
          <button>
            <SquareMinus
              strokeWidth={0.7}
              onClick={() => handleMinusClick(amount)}
              className={`responsive-icon cursor-pointer ${isEnabled ? "text-primary-dark" : "text-gray-300"}`}
            />
          </button>
        </div>
        <div
          className={`text-lg ${isEnabled ? "text-primary-dark" : "text-gray-300"}`}
        >
          {amount}
        </div>
        <div>
          <button>
            <SquarePlus
              strokeWidth={0.7}
              onClick={() => handlePlusClick(amount)}
              className={`responsive-icon cursor-pointer ${isEnabled ? "text-primary-dark" : "text-gray-300"}`}
            />
          </button>
        </div>
      </div>
    </>
  );
};

export default QuantitySelector;
