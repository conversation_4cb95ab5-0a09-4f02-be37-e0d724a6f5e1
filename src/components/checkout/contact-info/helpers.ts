/**
 * Helper functions for contact info component
 * Currently no complex logic needed, but following the pattern for consistency
 */

/**
 * Validates email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Gets error message for contact info fields
 */
export const getContactInfoErrorMessage = (
  error: { message?: string } | undefined,
): string | undefined => {
  return error?.message;
};
