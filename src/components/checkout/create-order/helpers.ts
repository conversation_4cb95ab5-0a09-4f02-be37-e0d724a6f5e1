import type { CheckoutFormData } from "@/pages/checkout/constants";

export const getFormDefaultValues = (
  userEmail?: string,
): Partial<CheckoutFormData> => ({
  contactInfo: {
    email: userEmail || "",
  },
  shippingAddress: {
    firstName: "",
    lastName: "",
    company: "",
    address_line1: "",
    address_line2: "",
    sub_district: "",
    district: "",
    city: "",
    postal_code: "",
    province: "",
    country: "Thailand",
  },
  taxInvoice: {
    wantTaxInvoice: false,
    personalInfo: {
      type: "individual",
      firstName: "",
      lastName: "",
      email: userEmail || "",
      phone: "",
    },
  },
  shippingMethod: "",
  deliveryAddressId: undefined,
  agreeToTerms: false,
});

export const getButtonText = (
  isCreatingOrder: boolean,
  isSubmitting: boolean,
): string => {
  if (isCreatingOrder) return "กำลังสร้างคำสั่งซื้อ...";
  if (isSubmitting) return "กำลังดำเนินการ...";
  return "สร้างคำสั่งซื้อ";
};
