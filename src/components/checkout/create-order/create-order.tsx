import React from "react";
import { <PERSON><PERSON> } from "@base/button";
import {
  ContactInfo,
  ShippingAddress,
  TaxInvoice,
  ShippingMethod,
  TermsConditions,
} from "@/components/checkout";
import type { CreateOrderProps } from "./types";
import { useCreateOrderForm } from "./hooks";
import { getButtonText } from "./helpers";
import type { CheckoutFormData } from "@/pages/checkout/constants";

export const CreateOrder: React.FC<CreateOrderProps> = ({
  isAuthenticated,
  userEmail,
  isCreatingOrder,
  orderError,
  onCreateOrder,
}) => {
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useCreateOrderForm(userEmail);

  const onSubmit = async (data: CheckoutFormData) => {
    await onCreateOrder(data);
  };

  return (
    <div className="space-y-6">
      <h1 className="text-[32px] font-medium">เช็คเอ้าท์</h1>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <ContactInfo
          control={control}
          errors={errors}
          isAuthenticated={isAuthenticated}
        />

        <ShippingAddress setValue={setValue} />

        <TaxInvoice
          control={control}
          errors={errors}
          watch={watch}
          setValue={setValue}
        />

        <ShippingMethod control={control} errors={errors} />

        <TermsConditions control={control} errors={errors} />

        {orderError && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{orderError}</p>
          </div>
        )}

        <Button
          type="submit"
          size="lg"
          className="w-full"
          disabled={isSubmitting || isCreatingOrder}
        >
          {getButtonText(isCreatingOrder, isSubmitting)}
        </Button>
      </form>
    </div>
  );
};
