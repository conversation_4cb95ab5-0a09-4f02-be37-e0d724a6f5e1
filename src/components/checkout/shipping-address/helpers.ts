import React from "react";

import type { UseFormSetValue } from "react-hook-form";

import type { CheckoutFormData } from "@/pages/checkout/constants";
import type { Address } from "@/types/address";

/**
 * Helper function to handle card clicks without interfering with form elements
 * Prevents double-triggering when clicking on radio buttons or other form elements
 */
export const handleCardClick = (
  e: React.MouseEvent<HTMLDivElement>,
  onSelect: (addressId: string) => void,
  addressId: string,
) => {
  // Prevent card click when clicking on form elements
  const target = e.target as HTMLElement;
  if (
    target.tagName === "INPUT" ||
    target.tagName === "SELECT" ||
    target.tagName === "TEXTAREA" ||
    target.tagName === "BUTTON" ||
    target.closest(
      'input, select, textarea, button, [role="combobox"], [role="listbox"]',
    )
  ) {
    return;
  }
  onSelect(addressId);
};

/**
 * Maps an address object to checkout form values
 */
export const mapAddressToCheckoutForm = (
  address: Address,
  setValue: UseFormSetValue<CheckoutFormData>,
) => {
  setValue("shippingAddress.firstName", address.first_name);
  setValue("shippingAddress.lastName", address.last_name);
  setValue("shippingAddress.company", "");
  setValue("shippingAddress.address_line1", address.address_line1);
  setValue("shippingAddress.address_line2", address.address_line2 || "");
  setValue("shippingAddress.sub_district", address.sub_district);
  setValue("shippingAddress.district", address.district);
  setValue("shippingAddress.city", address.city);
  setValue("shippingAddress.province", address.province);
  setValue("shippingAddress.postal_code", address.postal_code);
  setValue("shippingAddress.country", address.country);
};
