import { ApiService } from "@/services/api";
import { PAYMENT_METHOD_ENDPOINTS } from "@/services/api/constants";
import type { OmiseCard, OmiseCardsList, OmiseCustomer } from "@/types/omise";
import type {
  AlipayAccount,
  AlipayFormData,
  CreditCardFormData,
  CreditDebitCard,
  PaymentMethod,
  PaymentMethodApiData,
  PaymentMethodApiResponse,
  TrueMoneyFormData,
  TrueMoneyWallet,
} from "@/types/payment-methods";
import { PaymentMethodType } from "@/types/payment-methods";

import { maskPhoneNumber } from "@utils/mask.ts";

import { omiseApiService } from "./omise-api";

class PaymentMethodService {
  async addCreditCard(formData: CreditCardFormData): Promise<CreditDebitCard> {
    try {
      if (!formData.omiseToken) {
        throw new Error("No card token provided");
      }
      const token = formData.omiseToken;

      const response = await ApiService.post<{
        message: string;
        data: PaymentMethodApiData;
      }>(PAYMENT_METHOD_ENDPOINTS.CREATE_PAYMENT, {
        token: token,
        is_default: formData.isDefault,
      });

      if (response && response.data) {
        // Transform API response to CreditDebitCard format
        const apiData = response.data;
        const creditCard: CreditDebitCard = {
          id: apiData.id,
          type:
            apiData.brand.toLowerCase() === "visa"
              ? "credit_card"
              : "debit_card", // Simplified logic
          isDefault: apiData.is_default,
          isActive: true,
          displayName: `${apiData.brand.toUpperCase()} ****${apiData.last_four_digits}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          omiseToken: token,
          lastFourDigits: apiData.last_four_digits,
          cardBrand: apiData.brand.toLowerCase() as
            | "visa"
            | "mastercard"
            | "jcb"
            | "amex",
          expiryMonth: apiData.expiry_month.toString().padStart(2, "0"),
          expiryYear: apiData.expiry_year.toString(),
          holderName: apiData.name,
        };
        return creditCard;
      }

      let customerId = localStorage.getItem("omise_customer_id");

      if (!customerId) {
        const customerResponse = await omiseApiService.createCustomer(
          "<EMAIL>",
          "Customer created via web app",
        );

        const customer = customerResponse as OmiseCustomer;

        if (!customer || !customer.id) {
          throw new Error("Failed to create customer");
        }

        customerId = customer.id;
        localStorage.setItem("omise_customer_id", customerId as string);
      }

      if (!customerId) {
        throw new Error("No customer ID found");
      }
      const cardResponse = await omiseApiService.addCardToCustomer(
        customerId,
        token,
      );

      const card = cardResponse as OmiseCard;

      const creditCard: CreditDebitCard = {
        id: card.id,
        type: card.brand === "visa" ? "credit_card" : "debit_card", // Simplified logic
        isDefault: formData.isDefault,
        isActive: true,
        displayName:
          formData.displayName || `Card ending in ${card.last_digits}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        omiseToken: token,
        lastFourDigits: card.last_digits,
        cardBrand: card.brand as "visa" | "mastercard" | "jcb" | "amex",
        expiryMonth: card.expiration_month,
        expiryYear: card.expiration_year,
        holderName: card.name,
      };

      if (formData.isDefault) {
        await this.setAsDefault(creditCard.id);
      }

      return creditCard;
    } catch (error) {
      throw new Error(`Failed to add card: ${(error as Error).message}`);
    }
  }

  async addTrueMoneyWallet(
    formData: TrueMoneyFormData,
  ): Promise<TrueMoneyWallet> {
    try {
      const response = await ApiService.post<TrueMoneyWallet>(
        PAYMENT_METHOD_ENDPOINTS.TRUEMONEY,
        {
          phone_number: formData.phoneNumber,
          is_default: formData.isDefault,
          display_name:
            formData.displayName ||
            `TrueMoney (${maskPhoneNumber(formData.phoneNumber)})`,
        },
      );

      if (formData.isDefault) {
        await this.setAsDefault(response.id);
      }

      return response;
    } catch (error) {
      throw new Error(
        `Failed to add TrueMoney wallet: ${(error as Error).message}`,
      );
    }
  }

  async addAlipayAccount(formData: AlipayFormData): Promise<AlipayAccount> {
    try {
      const response = await ApiService.post<AlipayAccount>(
        PAYMENT_METHOD_ENDPOINTS.ALIPAY,
        {
          account_email: formData.accountEmail,
          is_default: formData.isDefault,
          display_name: formData.displayName || "Alipay Account",
        },
      );

      if (formData.isDefault) {
        await this.setAsDefault(response.id);
      }

      return response;
    } catch (error) {
      throw new Error(
        `Failed to add Alipay account: ${(error as Error).message}`,
      );
    }
  }

  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const response = await ApiService.get<PaymentMethodApiResponse>(
        PAYMENT_METHOD_ENDPOINTS.PAYMENT,
      );

      if (!response || !response.data) {
        return [];
      }

      // Transform API response to PaymentMethod format
      const paymentMethods: PaymentMethod[] = response.data.map(
        (card: PaymentMethodApiData) => {
          return {
            id: card.id,
            type: PaymentMethodType.CREDIT_CARD, // Simplified - treat all as credit cards
            isDefault: card.is_default, // Use API response field directly
            isActive: true,
            displayName: `${card.brand.toUpperCase()} ****${card.last_four_digits}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            omiseToken: "", // Not provided by this API
            lastFourDigits: card.last_four_digits,
            cardBrand: card.brand.toLowerCase() as
              | "visa"
              | "mastercard"
              | "jcb"
              | "amex",
            expiryMonth: card.expiry_month.toString().padStart(2, "0"),
            expiryYear: card.expiry_year.toString(),
            holderName: card.name,
          } as CreditDebitCard;
        },
      );

      return paymentMethods;
    } catch (error) {
      throw new Error(
        `Failed to get payment methods: ${(error as Error).message}`,
      );
    }
  }

  async setAsDefault(paymentMethodId: string): Promise<void> {
    try {
      const methods = await this.getPaymentMethods();
      const methodExists = methods.some((m) => m.id === paymentMethodId);

      if (!methodExists) {
        throw new Error(`Payment method with ID ${paymentMethodId} not found`);
      }

      localStorage.setItem("default_payment_method_id", paymentMethodId);
    } catch (error) {
      throw new Error(
        `Failed to set payment method as default: ${(error as Error).message}`,
      );
    }
  }

  async removePaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      const currentMethods = await this.getPaymentMethods();
      const methodToRemove = currentMethods.find(
        (m) => m.id === paymentMethodId,
      );

      if (methodToRemove?.isDefault && currentMethods.length > 1) {
        const nextDefault = currentMethods.find(
          (m) => m.id !== paymentMethodId,
        );
        if (nextDefault && nextDefault.id) {
          await this.setAsDefault(nextDefault.id);
        }
      }

      // Use the PAYMENT endpoint with DELETE method and include the ID
      await ApiService.delete(
        `${PAYMENT_METHOD_ENDPOINTS.PAYMENT}${paymentMethodId}/`,
      );
    } catch (error) {
      throw new Error(
        `Failed to remove payment method: ${(error as Error).message}`,
      );
    }
  }

  async getCustomerCards(customerId: string): Promise<OmiseCardsList> {
    try {
      return await omiseApiService.getCustomerCards(customerId);
    } catch (error) {
      throw new Error(
        `Failed to get customer cards: ${(error as Error).message}`,
      );
    }
  }

  async getCustomerCard(
    customerId: string,
    cardId: string,
  ): Promise<OmiseCard> {
    try {
      return await omiseApiService.getCustomerCard(customerId, cardId);
    } catch (error) {
      throw new Error(
        `Failed to get customer card: ${(error as Error).message}`,
      );
    }
  }
}

export const paymentMethodService = new PaymentMethodService();
