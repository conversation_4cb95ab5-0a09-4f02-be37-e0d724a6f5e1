import Layout from "../components/layout";
import HomePage from "../pages/home";
import NotFoundPage from "../pages/not-found";
import ProductsPage from "../pages/products";
import ProductDetailPage from "../pages/product-detail";
import RegisterPage from "../pages/register";
import { LoginPage } from "@/pages/login";
import { MyAccountPage } from "@/pages/my-account";
import VerifyCallback from "@/pages/callback/callback.tsx";
import ShoppingCartPage from "@/pages/shopping-cart";
import { CheckoutPage } from "@/pages/checkout";
import CustomerPage from "@/pages/poc/customer";

// Define routes with a nested structure
export const routes = [
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true, // This makes it the default route for "/"
        element: <HomePage />,
      },
      {
        path: "login",
        element: <LoginPage />,
      },
      {
        path: "register",
        element: <RegisterPage />,
      },
      {
        path: "products",
        element: <ProductsPage />,
      },
      {
        path: "products/:id",
        element: <ProductDetailPage />,
      },
      {
        path: "shopping-cart",
        element: <ShoppingCartPage />,
      },
      {
        path: "checkout",
        element: <CheckoutPage />,
      },
      {
        path: "verify",
        element: <VerifyCallback />,
      },
      {
        path: "my-account",
        element: <MyAccountPage />,
      },
      {
        path: "checkout",
        element: <CheckoutPage />,
      },
      {
        path: "poc/customer",
        element: <CustomerPage />,
      },
      {
        path: "*", // Catch-all route for 404 pages
        element: <NotFoundPage />,
      },
    ],
  },
];
