// Define interfaces for the user data and auth tokens
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  tier: string;
  tier_expiry: string | null;
  phone: string | null;
  avatar_url: string | null;
  age: number | null;
  gender: string | null;
  nationality: string | null;
  email_verified: boolean;
  provider: string;
  is_tier_active: boolean;
  date_joined: string;
  created_at: string;
  updated_at: string;
  username: string;
}

export interface AuthTokens {
  access: string | null;
  refresh: string | null;
}
