export interface CreateOrderRequest {
  delivery_provider: "shopee" | "flash";
  delivery_type: "1" | "2" | "3";
  delivery_address_id: number;
  recipient_name: string;
  phone: string;
  address_line1: string;
  address_line2?: string;
  sub_district: string;
  district: string;
  city: string;
  province: string;
  postal_code: string;
  country: "Thailand";
  use_tax: boolean;
  tax_type?: "personal" | "company";
  tax_first_name?: string;
  tax_last_name?: string;
  use_delivery_address: boolean;
  business_tax_id?: string;
  company_name?: string;
  tax_phone?: string;
  tax_email?: string;
  tax_address_line1?: string;
  tax_address_line2?: string;
  tax_sub_district?: string;
  tax_district?: string;
  tax_city?: string;
  tax_province?: string;
  tax_postal_code?: string;
  tax_country?: "Thailand";
}

export interface OrderItem {
  product_variant: number;
  quantity: number;
  price: string;
  sku: string;
}

export interface CreateOrderResponse {
  id: number;
  user: number;
  total_price: string;
  created_at: string;
  updated_at: string;
  items: OrderItem[];
}

// API wrapper response type
export interface ApiResponse<T> {
  message: string;
  data: T;
  status: number;
}

// Helper types for mapping
export interface CartItem {
  id: number;
  product_variant_id: number;
  quantity: number;
  price: number;
  sku: string;
}

export interface Cart {
  id: number;
  items: CartItem[];
  total_price: number;
}

export interface ShippingMethodData {
  provider: "shopee" | "flash";
  type: "1" | "2" | "3";
}

export interface PaymentMethodData {
  method: string;
  card_profile_id?: number;
  token?: string;
}

export interface ProcessPaymentRequest {
  order_id: number;
  payment_method: string;
  card_profile_id?: number;
  token?: string;
  source?: string;
  mobile_bank?: string;
  return_uri?: string;
}

export interface ProcessPaymentResponse {
  id: number;
  order_id: number;
  payment_method: string;
  amount: string;
  status: "pending" | "paid" | "failed" | "refunded";
  created_at: string;
  updated_at: string;
}
